/**
 * Project-related Types
 */

// Project entity
export interface Project {
  id: string;
  name: string;
  description: string;
  status: "active" | "completed" | "archived";
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// Project creation/update data
export interface CreateProjectData {
  name: string;
  description: string;
}

export interface UpdateProjectData {
  name?: string;
  description?: string;
  status?: "active" | "completed" | "archived";
}

// Project status types
export type ProjectStatus = "active" | "completed" | "archived";

// Project filters and search
export interface ProjectFilters {
  status?: ProjectStatus;
  userId?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}

// Project statistics
export interface ProjectStats {
  total: number;
  active: number;
  completed: number;
  archived: number;
  completionRate: number;
}