/**
 * API Response and Request Types
 */

// Generic API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Paginated response
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Pagination parameters
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// Sort parameters
export interface SortParams {
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Search parameters
export interface SearchParams {
  search?: string;
  filters?: Record<string, any>;
}

// Combined query parameters
export interface QueryParams extends PaginationParams, SortParams, SearchParams {}

// API error response
export interface ApiErrorResponse {
  success: false;
  error: string;
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Health check response
export interface HealthCheckResponse {
  status: "healthy" | "unhealthy";
  timestamp: string;
  services: {
    database: "up" | "down";
    analytics: "up" | "down";
    [key: string]: "up" | "down";
  };
  uptime: number;
  version: string;
}