/**
 * Authentication and User Types
 */

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role?: "admin" | "user"; // Optional since we're using email domain for admin check
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Authentication token types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

// Login/Register credential types
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Session payload for JWT
export interface SessionPayload {
  userId: string;
  email: string;
  role: string;
  expiresAt: number; // Unix timestamp
  [key: string]: any; // Index signature for JWT compatibility
}

// Authentication error types
export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}