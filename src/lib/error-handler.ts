import { toast } from "sonner";

export interface ErrorContext {
  userId?: string;
  action?: string;
  component?: string;
  metadata?: Record<string, any>;
}

export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: ErrorContext;

  constructor(
    message: string,
    code: string = "UNKNOWN_ERROR",
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: ErrorContext
  ) {
    super(message);
    this.name = "AppError";
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorCodes = {
  // Authentication errors
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  
  // Validation errors
  VALIDATION_ERROR: "VALIDATION_ERROR",
  INVALID_INPUT: "INVALID_INPUT",
  
  // Network errors
  NETWORK_ERROR: "NETWORK_ERROR",
  TIMEOUT_ERROR: "TIMEOUT_ERROR",
  
  // API errors
  API_ERROR: "API_ERROR",
  NOT_FOUND: "NOT_FOUND",
  
  // Application errors
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
  INTERNAL_ERROR: "INTERNAL_ERROR",
} as const;

export type ErrorCode = typeof errorCodes[keyof typeof errorCodes];

export class ErrorHandler {
  private static instance: ErrorHandler;

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  public handleError(error: Error | AppError, context?: ErrorContext): void {
    const isAppError = error instanceof AppError;
    const errorCode = isAppError ? error.code : errorCodes.UNKNOWN_ERROR;
    const statusCode = isAppError ? error.statusCode : 500;

    // Log error details
    console.error("Error handled:", {
      message: error.message,
      code: errorCode,
      statusCode,
      stack: error.stack,
      context: isAppError ? error.context : context,
    });

    // Show user-friendly message
    this.showUserMessage(error, errorCode);

    // Report to external service in production
    if (process.env.NODE_ENV === "production") {
      this.reportError(error, context);
    }
  }

  private showUserMessage(error: Error, code: ErrorCode): void {
    const userMessages: Record<ErrorCode, string> = {
      [errorCodes.UNAUTHORIZED]: "Please log in to continue",
      [errorCodes.FORBIDDEN]: "You don't have permission to perform this action",
      [errorCodes.TOKEN_EXPIRED]: "Your session has expired. Please log in again",
      [errorCodes.VALIDATION_ERROR]: "Please check your input and try again",
      [errorCodes.INVALID_INPUT]: "Invalid input provided",
      [errorCodes.NETWORK_ERROR]: "Network error. Please check your connection",
      [errorCodes.TIMEOUT_ERROR]: "Request timed out. Please try again",
      [errorCodes.API_ERROR]: "Server error. Please try again later",
      [errorCodes.NOT_FOUND]: "The requested resource was not found",
      [errorCodes.UNKNOWN_ERROR]: "An unexpected error occurred",
      [errorCodes.INTERNAL_ERROR]: "Internal error. Please try again later",
    };

    const message = userMessages[code] || "An unexpected error occurred";
    
    toast.error(message, {
      description: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }

  private reportError(error: Error, context?: ErrorContext): void {
    // TODO: Implement error reporting to external service
    // Example: Sentry, LogRocket, or custom logging service
    console.log("Error reported to external service:", { error, context });
  }

  public createError(
    message: string,
    code: ErrorCode,
    statusCode: number = 500,
    context?: ErrorContext
  ): AppError {
    return new AppError(message, code, statusCode, true, context);
  }
}

// Singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Utility functions for common error scenarios
export const createAuthError = (message: string = "Authentication required") =>
  errorHandler.createError(message, errorCodes.UNAUTHORIZED, 401);

export const createValidationError = (message: string = "Validation failed") =>
  errorHandler.createError(message, errorCodes.VALIDATION_ERROR, 400);

export const createNetworkError = (message: string = "Network error occurred") =>
  errorHandler.createError(message, errorCodes.NETWORK_ERROR, 0);

export const createApiError = (message: string = "API error occurred", statusCode: number = 500) =>
  errorHandler.createError(message, errorCodes.API_ERROR, statusCode);

// Global error handler for unhandled promise rejections
if (typeof window !== "undefined") {
  window.addEventListener("unhandledrejection", (event) => {
    console.error("Unhandled promise rejection:", event.reason);
    errorHandler.handleError(
      event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
      { action: "unhandled_promise_rejection" }
    );
  });
}