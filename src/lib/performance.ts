/**
 * Performance Monitoring Utilities
 * 
 * Utilities for tracking Core Web Vitals and performance metrics
 */

// Core Web Vitals types
export interface WebVital {
  id: string;
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  entries: PerformanceEntry[];
}

// Performance metrics
export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  
  // Custom metrics
  pageLoadTime?: number;
  domContentLoaded?: number;
  resourceLoadTime?: number;
  
  // User context
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  timestamp: number;
}

// Performance observer for tracking metrics
class PerformanceMonitor {
  private metrics: PerformanceMetrics = { timestamp: Date.now() };
  private observers: PerformanceObserver[] = [];

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
      this.trackNavigationTiming();
    }
  }

  private initializeObservers() {
    // Largest Contentful Paint
    this.observeMetric('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1] as PerformanceEntry;
      this.metrics.lcp = lastEntry.startTime;
      this.reportMetric('LCP', lastEntry.startTime);
    });

    // First Input Delay
    this.observeMetric('first-input', (entries) => {
      const firstEntry = entries[0] as PerformanceEventTiming;
      this.metrics.fid = firstEntry.processingStart - firstEntry.startTime;
      this.reportMetric('FID', this.metrics.fid);
    });

    // Cumulative Layout Shift
    this.observeMetric('layout-shift', (entries) => {
      let clsValue = 0;
      for (const entry of entries as PerformanceEntry[]) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.metrics.cls = clsValue;
      this.reportMetric('CLS', clsValue);
    });

    // First Contentful Paint
    this.observeMetric('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime;
        this.reportMetric('FCP', fcpEntry.startTime);
      }
    });

    // Navigation timing
    this.observeMetric('navigation', (entries) => {
      const navEntry = entries[0] as PerformanceNavigationTiming;
      this.metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
      this.metrics.domContentLoaded = navEntry.domContentLoadedEventEnd - navEntry.navigationStart;
      this.metrics.pageLoadTime = navEntry.loadEventEnd - navEntry.navigationStart;
      
      this.reportMetric('TTFB', this.metrics.ttfb);
      this.reportMetric('DOM_CONTENT_LOADED', this.metrics.domContentLoaded);
      this.reportMetric('PAGE_LOAD_TIME', this.metrics.pageLoadTime);
    });
  }

  private observeMetric(type: string, callback: (entries: PerformanceEntry[]) => void) {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ type, buffered: true });
      this.observers.push(observer);
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error);
    }
  }

  private trackNavigationTiming() {
    // Track navigation timing when page loads
    if (document.readyState === 'complete') {
      this.processNavigationTiming();
    } else {
      window.addEventListener('load', () => {
        this.processNavigationTiming();
      });
    }
  }

  private processNavigationTiming() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      this.metrics.ttfb = navigation.responseStart - navigation.requestStart;
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;
      this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.navigationStart;
    }
  }

  private reportMetric(name: string, value: number) {
    // Add user context
    this.metrics.url = window.location.href;
    this.metrics.userAgent = navigator.userAgent;
    
    // Report to analytics service
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(name, value);
    }
    
    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance Metric - ${name}:`, value);
    }
  }

  private sendToAnalytics(name: string, value: number) {
    // Send to PostHog or other analytics service
    if (typeof window !== 'undefined' && (window as any).posthog) {
      (window as any).posthog.capture('performance_metric', {
        metric_name: name,
        metric_value: value,
        url: window.location.href,
        user_agent: navigator.userAgent,
        timestamp: Date.now(),
      });
    }
    
    // You can also send to other services like Google Analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'performance_metric', {
        custom_parameter_1: name,
        custom_parameter_2: value,
      });
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Singleton instance
let performanceMonitor: PerformanceMonitor | null = null;

export const getPerformanceMonitor = (): PerformanceMonitor => {
  if (!performanceMonitor && typeof window !== 'undefined') {
    performanceMonitor = new PerformanceMonitor();
  }
  return performanceMonitor!;
};

// Utility functions for manual performance tracking
export const measurePerformance = {
  /**
   * Measure the execution time of a function
   */
  measure: async <T>(name: string, fn: () => Promise<T> | T): Promise<T> => {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;
      
      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
      
      // Report to analytics
      if (typeof window !== 'undefined' && (window as any).posthog) {
        (window as any).posthog.capture('custom_performance_measure', {
          operation: name,
          duration,
          timestamp: Date.now(),
        });
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      console.error(`Performance: ${name} failed after ${duration.toFixed(2)}ms`, error);
      throw error;
    }
  },

  /**
   * Mark a custom timing point
   */
  mark: (name: string) => {
    if (typeof window !== 'undefined') {
      performance.mark(name);
    }
  },

  /**
   * Measure between two marks
   */
  measureBetween: (name: string, startMark: string, endMark: string) => {
    if (typeof window !== 'undefined') {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name, 'measure')[0];
        console.log(`Performance: ${name} took ${measure.duration.toFixed(2)}ms`);
        return measure.duration;
      } catch (error) {
        console.warn(`Failed to measure between ${startMark} and ${endMark}:`, error);
        return 0;
      }
    }
    return 0;
  },
};

// React hook for performance monitoring
export const usePerformanceMonitor = () => {
  const monitor = getPerformanceMonitor();
  
  return {
    getMetrics: () => monitor?.getMetrics(),
    measure: measurePerformance.measure,
    mark: measurePerformance.mark,
    measureBetween: measurePerformance.measureBetween,
  };
};

// Initialize performance monitoring
export const initializePerformanceMonitoring = () => {
  if (typeof window !== 'undefined') {
    getPerformanceMonitor();
  }
};