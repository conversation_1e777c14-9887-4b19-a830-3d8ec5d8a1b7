/**
 * Theme Utilities
 * 
 * Centralized utilities for working with design tokens and theme values.
 * These utilities provide type-safe access to CSS custom properties.
 */

// Color token names (matches design-tokens.css)
export const colorTokens = {
  // Base colors
  background: "background",
  foreground: "foreground",
  
  // Surface colors
  card: "card",
  cardForeground: "card-foreground",
  popover: "popover",
  popoverForeground: "popover-foreground",
  
  // Brand colors
  brandLight: "brand-light",
  brandMedium: "brand-medium", 
  brandDark: "brand-dark",
  brandWhite: "brand-white",
  
  // Primary colors
  primary: "primary",
  primaryForeground: "primary-foreground",
  primaryLight: "primary-light",
  primaryDark: "primary-dark",
  
  // Secondary colors
  secondary: "secondary",
  secondaryForeground: "secondary-foreground",
  
  // Neutral colors
  muted: "muted",
  mutedForeground: "muted-foreground",
  accent: "accent",
  accentForeground: "accent-foreground",
  
  // Semantic colors
  destructive: "destructive",
  destructiveForeground: "destructive-foreground",
  
  // Border & input colors
  border: "border",
  input: "input",
  ring: "ring",
  
  // Chart colors
  chart1: "chart-1",
  chart2: "chart-2",
  chart3: "chart-3",
  chart4: "chart-4",
  chart5: "chart-5",
  
  // Sidebar colors
  sidebar: "sidebar",
  sidebarForeground: "sidebar-foreground",
  sidebarPrimary: "sidebar-primary",
  sidebarPrimaryForeground: "sidebar-primary-foreground",
  sidebarAccent: "sidebar-accent",
  sidebarAccentForeground: "sidebar-accent-foreground",
  sidebarBorder: "sidebar-border",
  sidebarRing: "sidebar-ring",
} as const;

export type ColorToken = typeof colorTokens[keyof typeof colorTokens];

// Spacing scale (matches design-tokens.css)
export const spacingScale = [0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32] as const;
export type SpacingValue = typeof spacingScale[number];

// Font size scale
export const fontSizeScale = ["xs", "sm", "base", "lg", "xl", "2xl", "3xl", "4xl", "5xl", "6xl"] as const;
export type FontSizeValue = typeof fontSizeScale[number];

// Border radius scale
export const radiusScale = ["none", "sm", "md", "lg", "xl", "2xl", "3xl", "full"] as const;
export type RadiusValue = typeof radiusScale[number];

// Shadow scale
export const shadowScale = ["2xs", "xs", "sm", "", "md", "lg", "xl", "2xl"] as const;
export type ShadowValue = typeof shadowScale[number];

/**
 * Get a CSS custom property value for a color token
 */
export const getThemeColor = (colorName: ColorToken): string => {
  return `var(--${colorName})`;
};

/**
 * Get a spacing value using the design system scale
 */
export const spacing = (size: SpacingValue): string => {
  return `var(--spacing-${size})`;
};

/**
 * Get a font size value using the design system scale
 */
export const fontSize = (size: FontSizeValue): string => {
  return `var(--font-size-${size})`;
};

/**
 * Get a border radius value using the design system scale
 */
export const radius = (size: RadiusValue): string => {
  return `var(--radius-${size})`;
};

/**
 * Get a shadow value using the design system scale
 */
export const shadow = (size: ShadowValue): string => {
  return `var(--shadow${size ? `-${size}` : ""})`;
};

/**
 * Get font family values
 */
export const fontFamily = {
  sans: "var(--font-sans)",
  serif: "var(--font-serif)", 
  mono: "var(--font-mono)",
} as const;

/**
 * Get transition values
 */
export const transition = {
  fast: "var(--transition-fast)",
  normal: "var(--transition-normal)",
  slow: "var(--transition-slow)",
  bounce: "var(--transition-bounce)",
  spring: "var(--transition-spring)",
} as const;

/**
 * Get z-index values
 */
export const zIndex = {
  dropdown: "var(--z-dropdown)",
  sticky: "var(--z-sticky)",
  fixed: "var(--z-fixed)",
  modalBackdrop: "var(--z-modal-backdrop)",
  modal: "var(--z-modal)",
  popover: "var(--z-popover)",
  tooltip: "var(--z-tooltip)",
  toast: "var(--z-toast)",
} as const;

/**
 * Breakpoint utilities
 */
export const breakpoints = {
  sm: "var(--breakpoint-sm)",
  md: "var(--breakpoint-md)",
  lg: "var(--breakpoint-lg)",
  xl: "var(--breakpoint-xl)",
  "2xl": "var(--breakpoint-2xl)",
} as const;

/**
 * Container max widths
 */
export const containerMaxWidth = {
  sm: "var(--container-sm)",
  md: "var(--container-md)",
  lg: "var(--container-lg)",
  xl: "var(--container-xl)",
  "2xl": "var(--container-2xl)",
} as const;

/**
 * Utility to create CSS-in-JS styles with theme tokens
 */
export const createThemeStyles = <T extends Record<string, any>>(styles: T): T => {
  return styles;
};

/**
 * Utility to get current theme (light/dark)
 */
export const getCurrentTheme = (): "light" | "dark" => {
  if (typeof window === "undefined") return "light";
  
  return document.documentElement.classList.contains("dark") ? "dark" : "light";
};

/**
 * Utility to check if dark mode is active
 */
export const isDarkMode = (): boolean => {
  return getCurrentTheme() === "dark";
};

/**
 * Color contrast utilities
 */
export const colorContrast = {
  /**
   * Get high contrast text color for a given background
   */
  getTextColor: (backgroundColor: ColorToken): ColorToken => {
    // Simple mapping - in a real app, you might calculate this dynamically
    const lightBackgrounds: ColorToken[] = [
      colorTokens.background,
      colorTokens.card,
      colorTokens.popover,
      colorTokens.muted,
      colorTokens.accent,
      colorTokens.primaryLight,
      colorTokens.brandLight,
      colorTokens.brandWhite,
    ];
    
    return lightBackgrounds.includes(backgroundColor) 
      ? colorTokens.foreground 
      : colorTokens.background;
  },
  
  /**
   * Get appropriate border color for a given background
   */
  getBorderColor: (backgroundColor: ColorToken): ColorToken => {
    return backgroundColor === colorTokens.background 
      ? colorTokens.border 
      : colorTokens.border;
  },
};

/**
 * Animation utilities
 */
export const animations = {
  appear: "var(--animation-appear)",
  appearZoom: "var(--animation-appear-zoom)",
} as const;

/**
 * Progress bar theme utilities
 */
export const progressTheme = {
  background: getThemeColor("muted"),
  fill: getThemeColor("primary"),
  border: getThemeColor("border"),
  text: getThemeColor("foreground"),
} as const;

/**
 * Export commonly used combinations
 */
export const commonStyles = {
  card: {
    backgroundColor: getThemeColor(colorTokens.card),
    color: getThemeColor(colorTokens.cardForeground),
    borderColor: getThemeColor(colorTokens.border),
    borderRadius: radius("lg"),
  },
  
  button: {
    primary: {
      backgroundColor: getThemeColor(colorTokens.primary),
      color: getThemeColor(colorTokens.primaryForeground),
      borderRadius: radius("md"),
    },
    
    secondary: {
      backgroundColor: getThemeColor(colorTokens.secondary),
      color: getThemeColor(colorTokens.secondaryForeground),
      borderRadius: radius("md"),
    },
    
    outline: {
      backgroundColor: "transparent",
      color: getThemeColor(colorTokens.foreground),
      borderColor: getThemeColor(colorTokens.border),
      borderRadius: radius("md"),
    },
  },
  
  input: {
    backgroundColor: getThemeColor(colorTokens.background),
    color: getThemeColor(colorTokens.foreground),
    borderColor: getThemeColor(colorTokens.input),
    borderRadius: radius("md"),
  },
} as const;