// Re-export types from domain-specific files for backward compatibility
export * from "@/types/auth";
export * from "@/types/project";
export * from "@/types/api";

// Admin Analytics types (keeping here as they're complex and specific)
export interface AnalyticsSummary {
  totalUsers: number;
  activeToday: number;
  newUsersThisWeek: number;
  usersWithFeedback: number;
  feedbackRate: number;
  averageSessionTime: number;
  totalRequestsToday: number;
  averageResponseTime: number;
  systemUptime: number;
}

export interface UserAnalytics {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  signupDate: string;
  referralCode: string;
  lastSignIn: string;
  hasSubmittedFeedback: boolean;
  actionCount: number;
  averageSessionTime: number;
  totalSessions: number;
  feedbackSummary: string;
}

export interface UserAnalyticsResponse {
  data: UserAnalytics[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ActivityMetrics {
  dau: number;
  wau: number;
  mau: number;
  yau: number;
  dauChange: number;
  wauChange: number;
  mauChange: number;
  yauChange: number;
  date: string;
}

export interface ActivityTrendData {
  date: string;
  activeUsers: number;
  newUsers: number;
  totalRequests: number;
  averageSessionTime: number;
}

export interface ActivityTrends {
  data: ActivityTrendData[];
  summary: {
    totalDays: number;
    averageActiveUsers: number;
    totalNewUsers: number;
    growthRate: number;
  };
}

export interface AdminHealthCheck {
  status: string;
  timestamp: string;
  services: {
    database: string;
    analytics: string;
  };
}

export interface UserAnalyticsFilters {
  page?: number;
  limit?: number;
  role?: string;
  hasSubmittedFeedback?: boolean;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface ActivityFilters {
  startDate?: string;
  endDate?: string;
  granularity?: "day" | "week" | "month" | "year";
}